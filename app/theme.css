:root {
  --background: oklch(0.9892 0.0053 286.3028);
  --foreground: oklch(0.3034 0.0570 285.3201);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3034 0.0570 285.3201);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3034 0.0570 285.3201);
  --primary: oklch(0.5505 0.1769 282.3031);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9341 0.0242 288.4759);
  --secondary-foreground: oklch(0.4295 0.1036 283.2487);
  --muted: oklch(0.9341 0.0242 288.4759);
  --muted-foreground: oklch(0.5505 0.1769 282.3031);
  --accent: oklch(0.9341 0.0242 288.4759);
  --accent-foreground: oklch(0.4295 0.1036 283.2487);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8966 0.0300 289.9581);
  --input: oklch(0.9341 0.0242 288.4759);
  --ring: oklch(0.5505 0.1769 282.3031);
  --chart-1: oklch(0.5505 0.1769 282.3031);
  --chart-2: oklch(0.5054 0.1680 282.2200);
  --chart-3: oklch(0.4592 0.1588 282.0896);
  --chart-4: oklch(0.4138 0.1482 281.5867);
  --chart-5: oklch(0.3673 0.1373 280.9309);
  --sidebar: oklch(0.9705 0.0120 291.2911);
  --sidebar-foreground: oklch(0.4295 0.1036 283.2487);
  --sidebar-primary: oklch(0.5505 0.1769 282.3031);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9341 0.0242 288.4759);
  --sidebar-accent-foreground: oklch(0.4295 0.1036 283.2487);
  --sidebar-border: oklch(0.8966 0.0300 289.9581);
  --sidebar-ring: oklch(0.5505 0.1769 282.3031);
  --font-sans: var(--font-geist-sans);
  --font-serif: var(--font-source-serif-4);
  --font-mono: var(--font-geist-mono);
  --radius: 0.75rem;
  --shadow-2xs: 0px 4px 8px 0px hsl(244.5378 58.0488% 59.8039% / 0.03);
  --shadow-xs: 0px 4px 8px 0px hsl(244.5378 58.0488% 59.8039% / 0.03);
  --shadow-sm: 0px 4px 8px 0px hsl(244.5378 58.0488% 59.8039% / 0.05), 0px 1px 2px -1px hsl(244.5378 58.0488% 59.8039% / 0.05);
  --shadow: 0px 4px 8px 0px hsl(244.5378 58.0488% 59.8039% / 0.05), 0px 1px 2px -1px hsl(244.5378 58.0488% 59.8039% / 0.05);
  --shadow-md: 0px 4px 8px 0px hsl(244.5378 58.0488% 59.8039% / 0.05), 0px 2px 4px -1px hsl(244.5378 58.0488% 59.8039% / 0.05);
  --shadow-lg: 0px 4px 8px 0px hsl(244.5378 58.0488% 59.8039% / 0.05), 0px 4px 6px -1px hsl(244.5378 58.0488% 59.8039% / 0.05);
  --shadow-xl: 0px 4px 8px 0px hsl(244.5378 58.0488% 59.8039% / 0.05), 0px 8px 10px -1px hsl(244.5378 58.0488% 59.8039% / 0.05);
  --shadow-2xl: 0px 4px 8px 0px hsl(244.5378 58.0488% 59.8039% / 0.13);
  --tracking-normal: 0rem;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1660 0.0351 284.5708);
  --foreground: oklch(0.7448 0.0702 287.3682);
  --card: oklch(0.2070 0.0448 285.7495);
  --card-foreground: oklch(0.9143 0.0434 286.9410);
  --popover: oklch(0.2070 0.0448 285.7495);
  --popover-foreground: oklch(0.9143 0.0434 286.9410);
  --primary: oklch(0.6591 0.1374 284.8737);
  --primary-foreground: oklch(0.1660 0.0351 284.5708);
  --secondary: oklch(0.4295 0.1036 283.2487);
  --secondary-foreground: oklch(0.9143 0.0434 286.9410);
  --muted: oklch(0.3034 0.0570 285.3201);
  --muted-foreground: oklch(0.6591 0.1374 284.8737);
  --accent: oklch(0.3034 0.0570 285.3201);
  --accent-foreground: oklch(0.9143 0.0434 286.9410);
  --destructive: oklch(0.3958 0.1331 25.7230);
  --destructive-foreground: oklch(0.9356 0.0309 17.7172);
  --border: oklch(0.3034 0.0570 285.3201);
  --input: oklch(0.3034 0.0570 285.3201);
  --ring: oklch(0.6591 0.1374 284.8737);
  --chart-1: oklch(0.6591 0.1374 284.8737);
  --chart-2: oklch(0.5968 0.1477 274.4311);
  --chart-3: oklch(0.5353 0.1514 270.4354);
  --chart-4: oklch(0.4756 0.1546 269.5186);
  --chart-5: oklch(0.4230 0.1531 268.4697);
  --sidebar: oklch(0.2070 0.0448 285.7495);
  --sidebar-foreground: oklch(0.7448 0.0702 287.3682);
  --sidebar-primary: oklch(0.6591 0.1374 284.8737);
  --sidebar-primary-foreground: oklch(0.1660 0.0351 284.5708);
  --sidebar-accent: oklch(0.3034 0.0570 285.3201);
  --sidebar-accent-foreground: oklch(0.9143 0.0434 286.9410);
  --sidebar-border: oklch(0.3034 0.0570 285.3201);
  --sidebar-ring: oklch(0.6591 0.1374 284.8737);
  --font-sans: var(--font-geist-sans);
  --font-serif: var(--font-source-serif-4);
  --font-mono: var(--font-geist-mono);
  --radius: 0.75rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}