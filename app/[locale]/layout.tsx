import "@/app/globals.css";

import { getMessages, getTranslations } from "next-intl/server";
import { locales } from "@/i18n/locale";

import { AppContextProvider } from "@/contexts/app";
import { Inter as FontSans } from "next/font/google";
import { Metadata } from "next";
import { NextAuthSessionProvider } from "@/auth/session";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider } from "@/providers/theme";
import { cn } from "@/lib/utils";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations();

  return {
    title: {
      template: `%s`,
      default: t("metadata.title") || "",
    },
    description: t("metadata.description") || "",
    keywords: t("metadata.keywords") || "",
    openGraph: {
      title: t("metadata.title") || "",
      description: t("metadata.description") || "",
      type: "website",
      locale: locale,
      url: process.env.NEXT_PUBLIC_WEB_URL,
      siteName: "AI ASMR Generator",
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/logo.png`,
          width: 1200,
          height: 630,
          alt: "AI ASMR Generator - Make AI ASMR Video With Veo 3",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: t("metadata.title") || "",
      description: t("metadata.description") || "",
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/logo.png`],
    },
  };
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  const messages = await getMessages();
  const webUrl = process.env.NEXT_PUBLIC_WEB_URL || "";
  const googleAdsenseCode = process.env.NEXT_PUBLIC_GOOGLE_ADCODE || "";
  const clarityProjectId = process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID || "";

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        {googleAdsenseCode && (
          <meta name="google-adsense-account" content={googleAdsenseCode} />
        )}
        {/* Google AdSense */}
      {googleAdsenseCode && (
          <script
            async
            src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${googleAdsenseCode}`}
            crossOrigin="anonymous"
          />
        )}

        <link rel="icon" href="/favicon.ico" />

        {/* JSON-LD Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@graph": [
                {
                  "@type": "WebSite",
                  "@id": `${webUrl}/#website`,
                  "url": webUrl,
                  "name": "AI ASMR Generator",
                  "description": "AI ASMR video generator for creating relaxing ASMR content using advanced AI technology",
                  "potentialAction": [
                    {
                      "@type": "SearchAction",
                      "target": {
                        "@type": "EntryPoint",
                        "urlTemplate": `${webUrl}/?q={search_term_string}`
                      },
                      "query-input": "required name=search_term_string"
                    }
                  ]
                },
                {
                  "@type": "Organization",
                  "@id": `${webUrl}/#organization`,
                  "name": "AI ASMR Generator",
                  "url": webUrl,
                  "logo": {
                    "@type": "ImageObject",
                    "url": `${webUrl}/logo.png`,
                    "width": 512,
                    "height": 512
                  },
                  "contactPoint": {
                    "@type": "ContactPoint",
                    "email": "<EMAIL>",
                    "contactType": "customer service"
                  },
                  "sameAs": []
                },
                {
                  "@type": "SoftwareApplication",
                  "@id": `${webUrl}/#software`,
                  "name": "AI ASMR Generator",
                  "applicationCategory": "WebApplication",
                  "operatingSystem": "Any",
                  "description": "AI ASMR video generator that creates relaxing ASMR content using Google's Veo 3 technology",
                  "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "USD",
                    "availability": "https://schema.org/InStock"
                  },
                  "featureList": [
                    "AI ASMR Video Generation",
                    "Glass Fruit Slicing ASMR",
                    "Whisper Therapy Videos",
                    "Relaxation Content Creation",
                    "ASMR Trigger Optimization"
                  ],
                  "screenshot": `${webUrl}/imgs/features/1.png`,
                  "url": webUrl,
                  "publisher": {
                    "@id": `${webUrl}/#organization`
                  }
                }
              ]
            })
          }}
        />

        {locales &&
          locales.map((loc) => (
            <link
              key={loc}
              rel="alternate"
              hrefLang={loc}
              href={`${webUrl}${loc === "en" ? "" : `/${loc}`}/`}
            />
          ))}
        <link rel="alternate" hrefLang="x-default" href={webUrl} />
      </head>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased overflow-x-hidden",
          fontSans.variable
        )}
      >
        <NextIntlClientProvider messages={messages}>
          <NextAuthSessionProvider>
            <AppContextProvider>
              <ThemeProvider attribute="class" disableTransitionOnChange>
                {children}
              </ThemeProvider>
            </AppContextProvider>
          </NextAuthSessionProvider>
        </NextIntlClientProvider>

        {clarityProjectId && (
          <script
            type="text/javascript"
            dangerouslySetInnerHTML={{
              __html: `
                (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "${clarityProjectId}");
              `,
            }}
          />
        )}
      </body>
    </html>
  );
}
