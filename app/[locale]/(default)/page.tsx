import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import Pricing from "@/components/blocks/pricing";
import PromptExamples from "@/components/blocks/prompt-examples";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import VideoGenerator from "@/components/blocks/video-generator";
import { getVeo3VideoGeneratorPage } from "@/services/page";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getVeo3VideoGeneratorPage(locale);

  // 从页面数据中获取视频生成器的文案
  const videoGeneratorTexts = page.generator;

  // 从页面数据中获取示例的多语言文案
  const examplesConfig = {
    copyButton: videoGeneratorTexts.copyButton,
    successCopied: videoGeneratorTexts.successCopied,
    errorCopyFailed: videoGeneratorTexts.copyFailed,
  };

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      {/* Veo-3 视频生成器 */}
      <VideoGenerator 
        texts={videoGeneratorTexts}
        templates={page.generator.templates}
      />
      {/* {page.branding && <Branding section={page.branding} />} */}
      {page.examples && (
        <PromptExamples 
          section={page.examples}
          config={examplesConfig}
        />
      )}
      {page.usage && <Feature3 section={page.usage} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.feature && <Feature section={page.feature} />}
      {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
