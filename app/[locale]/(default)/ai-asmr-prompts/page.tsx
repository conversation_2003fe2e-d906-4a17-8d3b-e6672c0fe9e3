import Hero from "@/components/blocks/hero";
import CTA from "@/components/blocks/cta";
import PromptExamples from "@/components/blocks/prompt-examples";
import { getAIASMRPromptsPage } from "@/services/page";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/ai-asmr-prompts`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/ai-asmr-prompts`;
  }

  const page = await getAIASMRPromptsPage(locale);

  return {
    title: page.title,
    description: page.description,
    keywords: "",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function AIASMRPromptsPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getAIASMRPromptsPage(locale);
  const t = await getTranslations("ai_asmr_prompts");

  const examplesConfig = {
    copyButton: t("copyButton"),
    successCopied: t("successCopied"),
    errorCopyFailed: t("errorCopyFailed"),
  };

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      {page.examples && (
        <PromptExamples 
          section={page.examples}
          config={examplesConfig}
          className="py-16"
        />
      )}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
} 