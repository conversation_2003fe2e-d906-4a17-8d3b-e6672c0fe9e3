import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getBlogPostsByCategory, getBlogCategories } from '@/lib/blog';
import { getBlogPage } from '@/services/page';
import { BlogCard } from '@/components/blog/blog-card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface CategoryPageProps {
  params: Promise<{ locale: string; category: string }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; category: string }>;
}): Promise<Metadata> {
  const { locale, category } = await params;
  const categories = await getBlogCategories(locale);
  const categoryInfo = categories[category];
  const page = await getBlogPage(locale);

  if (!categoryInfo) {
    return {
      title: 'Category Not Found',
    };
  }

  const title = `${categoryInfo.name} - ${page.category.title_suffix}`;
  const description = categoryInfo.description;

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/blog/category/${category}`;
  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/blog/category/${category}`;
  }

  return {
    title,
    description,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
    },
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { locale, category } = await params;
  const posts = await getBlogPostsByCategory(category, locale);
  const categories = await getBlogCategories(locale);
  const categoryInfo = categories[category];
  const page = await getBlogPage(locale);

  if (!categoryInfo) {
    notFound();
  }

  const backUrl = locale === 'en' ? '/blog' : `/${locale}/blog`;

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 返回链接 */}
        <div className="mb-6">
          <Link
            href={backUrl}
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            {page.category.back_to_blog}
          </Link>
        </div>

        {/* 分类标题 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Badge 
              variant="secondary" 
              className="text-lg px-4 py-2"
              style={{ backgroundColor: `${categoryInfo.color}20`, color: categoryInfo.color }}
            >
              {categoryInfo.name}
            </Badge>
          </div>
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            {categoryInfo.name}
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {categoryInfo.description}
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            {posts.length} {page.category.posts_count}
          </p>
        </div>

        {/* 文章列表 */}
        {posts.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {page.category.no_posts}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {posts.map((post) => (
              <BlogCard
                key={post.slug}
                post={post}
                locale={locale}
                variant="default"
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
