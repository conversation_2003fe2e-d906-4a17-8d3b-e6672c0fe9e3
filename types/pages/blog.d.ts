export interface BlogPageTranslations {
  title: string;
  description: string;
  main: {
    title: string;
    description: string;
    featured_posts: string;
    latest_posts: string;
    filtered_results: string;
    no_posts: string;
    current_filter: string;
    category_prefix: string;
    tag_prefix: string;
    clear_filters: string;
    categories: string;
    tags: string;
    all: string;
  };
  category: {
    title_suffix: string;
    back_to_blog: string;
    posts_count: string;
    no_posts: string;
  };
  tag: {
    title_prefix: string;
    title_suffix: string;
    description_prefix: string;
    back_to_blog: string;
    posts_count: string;
    no_posts: string;
  };
}
