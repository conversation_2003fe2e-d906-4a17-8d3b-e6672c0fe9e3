import { BaseSection } from "../blocks/base";
import { HeroSection } from "../blocks/hero";
import { SectionType } from "../blocks/section";
import { BrandingSection } from "../blocks/footer";
import { FAQSection } from "../blocks/section";
import { CTASection } from "../blocks/section";
import { PricingSection } from "../blocks/pricing";

// 视频示例项类型，扩展PromptExample
export interface VideoExampleItem {
  id: string;
  title: string;
  prompt: string;
  videoUrl: string;
  thumbnail?: string;
}

// 视频示例区块类型，继承PromptExamplesSection
export interface VideoExamplesSection extends BaseSection {
  name: "examples";
  title: string;
  label: string;
  description: string;
  items: VideoExampleItem[];
}

// 视频模板类型
export interface VideoTemplate {
  id: string;
  title: string;
  prompt: string;
  exampleVideoUrl: string;
}

// 视频生成器文案类型
export interface VideoGeneratorTexts {
  title: string;
  subtitle: string;
  promptLabel: string;
  promptInputLabel: string;
  templatesLabel: string;
  promptPlaceholder: string;
  modelLabel: string;
  imageLabel: string;
  imagePlaceholder: string;
  generateButton: string;
  generating: string;
  generatingText: string;
  copyButton: string;
  downloadButton: string;
  shareButton: string;
  errorNoPrompt: string;
  errorGenerateFailed: string;
  successGenerated: string;
  successCopied: string;
  selectImage: string;
  resultLabel: string;
  resultPlaceholder: string;
  generationFailed: string;
  modelStandard: string;
  modelFast: string;
  errorCreditsNotEnough: string;
  errorUnauthorized: string;
  taskSubmitted: string;
  taskProcessing: string;
  statusQueryFailed: string;
  copyFailed: string;
  shareTitle: string;
  generationTimeout: string;
  uploadingImage: string;
  submittingTask: string;
  taskId: string;
  retryButton: string;
  errorImageTooLarge: string;
  errorInvalidImage: string;
  errorTaskIdFailed: string;
  referenceImageAlt: string;
  downloadPreparing: string;
  downloadFailed: string;
  downloadSuccess: string;
  downloadError: string;
  templates: VideoTemplate[];
}

// Veo3视频生成器页面类型
export interface Veo3VideoGeneratorPage {
  template: string;
  theme: string;
  title: string;
  description: string;
  header: {
    brand: {
      title: string;
      logo: {
        src: string;
        alt: string;
      };
      url: string;
    };
    nav: {
      items: Array<{
        title: string;
        url: string;
        icon: string;
      }>;
      buttons: Array<{
        title: string;
        variant: string;
        href: string;
      }>;
    };
    show_sign: boolean;
    show_theme: boolean;
    show_locale: boolean;
  };
  hero: HeroSection;
  branding: BrandingSection;
  examples: VideoExamplesSection;
  generator: VideoGeneratorTexts;
  introduce: SectionType;
  benefit: SectionType;
  usage: SectionType;
  feature: SectionType;
  stats?: SectionType;
  pricing?: PricingSection;
  testimonial?: SectionType;
  faq: FAQSection;
  cta: CTASection;
} 