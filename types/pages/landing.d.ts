import { Header } from "@/types/blocks/header";
import { Hero } from "@/types/blocks/hero";
import { Section } from "@/types/blocks/section";
import { Footer } from "@/types/blocks/footer";
import { Pricing } from "@/types/blocks/pricing";
import { TipsSection } from "@/types/blocks/tips";

export interface LandingPage {
  header?: Header;
  hero?: Hero;
  branding?: Section;
  tips?: TipsSection;
  introduce?: Section;
  benefit?: Section;
  usage?: Section;
  feature?: Section;
  showcase?: Section;
  stats?: Section;
  pricing?: Pricing;
  testimonial?: Section;
  faq?: Section;
  cta?: Section;
  footer?: Footer;
}

export interface PricingPage {
  title?: string;
  description?: string;
  pricing?: Pricing;
  faq?: Section;
}

export interface ShowcasePage {
  title?: string;
  description?: string;
  showcase?: Section;
}
