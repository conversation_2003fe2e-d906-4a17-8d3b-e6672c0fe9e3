export interface BlogFrontmatter {
  title: string;
  description: string;
  date: string;
  author: string;
  category: string;
  tags: string[];
  slug?: string;
  template?: BlogTemplate;
  coverImage?: string;
  published?: boolean;
  featured?: boolean;
  seo?: {
    keywords?: string[];
    ogImage?: string;
    ogDescription?: string;
  };
  style?: {
    headerStyle?: 'clean' | 'gradient' | 'minimal' | 'image';
    contentWidth?: 'narrow' | 'normal' | 'wide';
    showToc?: boolean;
    showAuthor?: boolean;
    showDate?: boolean;
    showTags?: boolean;
    showCategory?: boolean;
  };
}

export type BlogTemplate = 
  | 'default'
  | 'tutorial'
  | 'product'
  | 'technical'
  | 'minimal';

export interface BlogPost {
  slug: string;
  frontmatter: BlogFrontmatter;
  content: string;
  excerpt?: string;
  readingTime?: number;
  locale: string;
}

export interface BlogCategory {
  name: string;
  description: string;
  color: string;
}

export interface BlogCategories {
  [key: string]: BlogCategory;
}

export interface BlogListItem {
  slug: string;
  title: string;
  description: string;
  date: string;
  author: string;
  category: string;
  tags: string[];
  coverImage?: string;
  excerpt?: string;
  readingTime?: number;
  featured?: boolean;
}

export interface BlogPageProps {
  posts: BlogListItem[];
  categories: BlogCategories;
  currentCategory?: string;
  currentTag?: string;
  pagination?: {
    currentPage: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface BlogTemplateConfig {
  layout: 'single-column' | 'with-sidebar' | 'wide';
  headerStyle: 'clean' | 'gradient' | 'minimal' | 'step-indicator';
  typography: 'comfortable' | 'readable' | 'code-friendly';
  spacing: 'compact' | 'normal' | 'loose';
  features?: string[];
}
