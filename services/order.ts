import {
  CreditsTransType,
  increaseCredits,
  updateCreditForOrder,
} from "./credit";
import { findOrderByOrderNo, updateOrderStatus } from "@/models/order";
import { getIsoTimestr, getOneYearLaterTimestr } from "@/lib/time";

import Stripe from "stripe";
import { updateAffiliateForOrder } from "./affiliate";

export async function handleOrderSession(session: Stripe.Checkout.Session) {
  try {
    if (
      !session ||
      !session.metadata ||
      !session.metadata.order_no ||
      session.payment_status !== "paid"
    ) {
      throw new Error("invalid session");
    }

    const order_no = session.metadata.order_no;
    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== "created") {
      throw new Error("invalid order");
    }

    // 验证实际支付金额是否与订单金额一致
    const actualAmount = session.amount_total; // Stripe 实际收到的金额（分为单位）
    const expectedAmount = order.amount; // 订单预期金额（分为单位）
    
    if (actualAmount !== expectedAmount) {
      console.log("Payment amount mismatch", {
        order_no,
        expected: expectedAmount,
        actual: actualAmount
      });
      throw new Error("payment amount mismatch");
    }

    // 验证货币是否一致
    if (session.currency?.toLowerCase() !== order.currency?.toLowerCase()) {
      console.log("Currency mismatch", {
        order_no,
        expected: order.currency,
        actual: session.currency
      });
      throw new Error("currency mismatch");
    }

    const paid_at = getIsoTimestr();
    const paid_email = session.customer_details?.email || session.customer_email || "";
    const paid_detail = JSON.stringify(session);

    await updateOrderStatus(order_no, "paid", paid_at, paid_email, paid_detail);

    if (order.user_uuid) {
      if (order.credits > 0) {
        await updateCreditForOrder(order);
      }
      await updateAffiliateForOrder(order);
    }

    console.log("handle order session successed: ", order_no, paid_at, paid_email);
  } catch (e) {
    console.log("handle order session failed: ", e);
    throw e;
  }
}
