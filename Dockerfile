FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat && yarn global add pnpm

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json pnpm-lock.yaml* ./
RUN pnpm i --frozen-lockfile

# Rebuild the source code only when needed
FROM deps AS builder

WORKDIR /app

# -----------------------------------------------------------------------------
# Build Arguments for NEXT_PUBLIC_* variables
# -----------------------------------------------------------------------------
ARG NEXT_PUBLIC_WEB_URL
ARG NEXT_PUBLIC_PROJECT_NAME
ARG NEXT_PUBLIC_AUTH_GOOGLE_ID
ARG NEXT_PUBLIC_AUTH_GOOGLE_ENABLED
ARG NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED
ARG NEXT_PUBLIC_AUTH_GITHUB_ENABLED
ARG NEXT_PUBLIC_GOOGLE_ANALYTICS_ID
ARG NEXT_PUBLIC_OPENPANEL_CLIENT_ID
ARG NEXT_PUBLIC_PLAUSIBLE_DOMAIN
ARG NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL
ARG NEXT_PUBLIC_PAY_SUCCESS_URL
ARG NEXT_PUBLIC_PAY_FAIL_URL
ARG NEXT_PUBLIC_PAY_CANCEL_URL
ARG NEXT_PUBLIC_LOCALE_DETECTION
ARG NEXT_PUBLIC_DEFAULT_THEME
ARG NEXT_PUBLIC_GOOGLE_ADCODE
ARG NEXT_PUBLIC_CLARITY_PROJECT_ID

# Set environment variables for build
ENV NEXT_PUBLIC_WEB_URL=${NEXT_PUBLIC_WEB_URL}
ENV NEXT_PUBLIC_PROJECT_NAME=${NEXT_PUBLIC_PROJECT_NAME}
ENV NEXT_PUBLIC_AUTH_GOOGLE_ID=${NEXT_PUBLIC_AUTH_GOOGLE_ID}
ENV NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=${NEXT_PUBLIC_AUTH_GOOGLE_ENABLED}
ENV NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=${NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED}
ENV NEXT_PUBLIC_AUTH_GITHUB_ENABLED=${NEXT_PUBLIC_AUTH_GITHUB_ENABLED}
ENV NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=${NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}
ENV NEXT_PUBLIC_OPENPANEL_CLIENT_ID=${NEXT_PUBLIC_OPENPANEL_CLIENT_ID}
ENV NEXT_PUBLIC_PLAUSIBLE_DOMAIN=${NEXT_PUBLIC_PLAUSIBLE_DOMAIN}
ENV NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL=${NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL}
ENV NEXT_PUBLIC_PAY_SUCCESS_URL=${NEXT_PUBLIC_PAY_SUCCESS_URL}
ENV NEXT_PUBLIC_PAY_FAIL_URL=${NEXT_PUBLIC_PAY_FAIL_URL}
ENV NEXT_PUBLIC_PAY_CANCEL_URL=${NEXT_PUBLIC_PAY_CANCEL_URL}
ENV NEXT_PUBLIC_LOCALE_DETECTION=${NEXT_PUBLIC_LOCALE_DETECTION}
ENV NEXT_PUBLIC_DEFAULT_THEME=${NEXT_PUBLIC_DEFAULT_THEME}
ENV NEXT_PUBLIC_GOOGLE_ADCODE=${NEXT_PUBLIC_GOOGLE_ADCODE}
ENV NEXT_PUBLIC_CLARITY_PROJECT_ID=${NEXT_PUBLIC_CLARITY_PROJECT_ID}

COPY . .
RUN pnpm build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs && \
    mkdir .next && \
    chown nextjs:nodejs .next

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV NODE_ENV production
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
