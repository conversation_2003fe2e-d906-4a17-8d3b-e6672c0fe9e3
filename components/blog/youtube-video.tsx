import { cn } from '@/lib/utils';

interface YouTubeVideoProps {
  videoId: string;
  title?: string;
  caption?: string;
  width?: number;
  height?: number;
  className?: string;
  autoplay?: boolean;
}

// 从各种YouTube URL格式中提取视频ID
function extractVideoId(url: string): string {
  // 如果已经是视频ID，直接返回
  if (!/^https?:\/\//.test(url)) {
    return url;
  }

  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/shorts\/)([^&\n?#]+)/,
    /youtube\.com\/embed\/([^&\n?#]+)/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }

  throw new Error(`Invalid YouTube URL: ${url}`);
}

export function YouTubeVideo({ 
  videoId, 
  title,
  caption, 
  width = 800, 
  height = 450,
  className,
  autoplay = false 
}: YouTubeVideoProps) {
  // 提取视频ID（支持URL或直接传入ID）
  const extractedVideoId = extractVideoId(videoId);
  
  // 构建YouTube嵌入URL
  const embedUrl = `https://www.youtube-nocookie.com/embed/${extractedVideoId}?rel=0&modestbranding=1${autoplay ? '&autoplay=1' : ''}`;

  return (
    <figure className={cn('my-6', className)}>
      <div className="relative overflow-hidden rounded-lg bg-muted">
        <div className="aspect-video">
          <iframe
            src={embedUrl}
            title={title || 'YouTube video'}
            width={width}
            height={height}
            className="w-full h-full"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
          />
        </div>
      </div>
      {caption && (
        <figcaption className="text-sm text-muted-foreground text-center mt-2 italic">
          {caption}
        </figcaption>
      )}
    </figure>
  );
}
