import Link from 'next/link';
import Image from 'next/image';
import { Calendar, Clock, User, Tag } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import type { BlogListItem } from '@/types/blog';

interface BlogCardProps {
  post: BlogListItem;
  locale: string;
  showExcerpt?: boolean;
  variant?: 'default' | 'featured' | 'compact';
}

export function BlogCard({ 
  post, 
  locale, 
  showExcerpt = true, 
  variant = 'default' 
}: BlogCardProps) {
  const blogUrl = locale === 'en' 
    ? `/blog/${post.slug}` 
    : `/${locale}/blog/${post.slug}`;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (variant === 'compact') {
    return (
      <Link href={blogUrl} className="group block">
        <Card className="h-full transition-all duration-200 hover:shadow-md border-0 bg-transparent">
          <CardContent className="p-4">
            <div className="flex items-start gap-4">
              {post.coverImage && (
                <div className="flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden">
                  <Image
                    src={post.coverImage}
                    alt={post.title}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                  />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-sm line-clamp-2 text-foreground group-hover:text-primary transition-colors">
                  {post.title}
                </h3>
                <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {formatDate(post.date)}
                  </span>
                  {post.readingTime && (
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {Math.ceil(post.readingTime)} min
                    </span>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </Link>
    );
  }

  if (variant === 'featured') {
    return (
      <Link href={blogUrl} className="group block">
        <Card className="h-full transition-all duration-200 hover:shadow-lg border-0 bg-gradient-to-br from-primary/5 to-secondary/5">
          <div className="relative">
            {post.coverImage && (
              <div className="aspect-[16/9] overflow-hidden rounded-t-lg">
                <Image
                  src={post.coverImage}
                  alt={post.title}
                  width={600}
                  height={338}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                />
              </div>
            )}
            <Badge className="absolute top-4 left-4 bg-primary text-primary-foreground">
              Featured
            </Badge>
          </div>
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary" className="text-xs">
                {post.category}
              </Badge>
              {post.featured && (
                <Badge variant="outline" className="text-xs">
                  ⭐ Featured
                </Badge>
              )}
            </div>
            <h2 className="text-xl font-bold line-clamp-2 text-foreground group-hover:text-primary transition-colors">
              {post.title}
            </h2>
          </CardHeader>
          <CardContent className="pt-0">
            {showExcerpt && post.excerpt && (
              <p className="text-muted-foreground text-sm line-clamp-3 mb-4">
                {post.excerpt}
              </p>
            )}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <User className="w-4 h-4" />
                  {post.author}
                </span>
                <span className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {formatDate(post.date)}
                </span>
              </div>
              {post.readingTime && (
                <span className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {Math.ceil(post.readingTime)} min read
                </span>
              )}
            </div>
            {post.tags.length > 0 && (
              <div className="flex items-center gap-2 mt-3">
                <Tag className="w-3 h-3 text-muted-foreground" />
                <div className="flex flex-wrap gap-1">
                  {post.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {post.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{post.tags.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </Link>
    );
  }

  // Default variant
  return (
    <Link href={blogUrl} className="group block">
      <Card className="h-full transition-all duration-200 hover:shadow-md">
        {post.coverImage && (
          <div className="aspect-[16/9] overflow-hidden rounded-t-lg">
            <Image
              src={post.coverImage}
              alt={post.title}
              width={400}
              height={225}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          </div>
        )}
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2 mb-2">
            <Badge variant="secondary" className="text-xs">
              {post.category}
            </Badge>
            {post.featured && (
              <Badge variant="outline" className="text-xs">
                ⭐ Featured
              </Badge>
            )}
          </div>
          <h3 className="text-lg font-semibold line-clamp-2 text-foreground group-hover:text-primary transition-colors">
            {post.title}
          </h3>
        </CardHeader>
        <CardContent className="pt-0">
          {showExcerpt && post.excerpt && (
            <p className="text-muted-foreground text-sm line-clamp-3 mb-4">
              {post.excerpt}
            </p>
          )}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-3">
              <span className="flex items-center gap-1">
                <User className="w-4 h-4" />
                {post.author}
              </span>
              <span className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {formatDate(post.date)}
              </span>
            </div>
            {post.readingTime && (
              <span className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {Math.ceil(post.readingTime)} min
              </span>
            )}
          </div>
          {post.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-3">
              {post.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {post.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{post.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  );
}
