'use client';

import { useState } from 'react';
import { Co<PERSON>, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface CodeBlockProps {
  children: React.ReactNode;
  className?: string;
  language?: string;
}

export function CodeBlock({ children, className, language }: CodeBlockProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    const code = extractTextFromChildren(children);
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  // 提取文本内容的辅助函数
  const extractTextFromChildren = (children: React.ReactNode): string => {
    if (typeof children === 'string') {
      return children;
    }
    if (Array.isArray(children)) {
      return children.map(extractTextFromChildren).join('');
    }
    if (children && typeof children === 'object' && 'props' in children) {
      return extractTextFromChildren((children as any).props.children);
    }
    return '';
  };

  // 检测语言
  const detectedLanguage = language || 
    className?.replace('language-', '') || 
    'text';

  return (
    <div className="relative group my-6">
      {/* 语言标签和复制按钮 */}
      <div className="flex items-center justify-between bg-muted border border-b-0 rounded-t-lg px-4 py-2">
        <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
          {detectedLanguage}
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          {copied ? (
            <Check className="w-3 h-3 text-green-600" />
          ) : (
            <Copy className="w-3 h-3" />
          )}
        </Button>
      </div>
      
      {/* 代码内容 */}
      <pre className={cn(
        'overflow-x-auto bg-muted/50 border rounded-b-lg p-4 text-sm',
        'scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent',
        className
      )}>
        <code className="font-mono text-sm leading-relaxed">
          {children}
        </code>
      </pre>
    </div>
  );
}
