import Image from 'next/image';
import { cn } from '@/lib/utils';

interface BlogImageProps {
  src: string;
  alt: string;
  caption?: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

export function BlogImage({ 
  src, 
  alt, 
  caption, 
  width = 800, 
  height = 400,
  className,
  priority = false 
}: BlogImageProps) {
  // 处理 CDN URL
  const imageUrl = src.startsWith('http') 
    ? src 
    : `${process.env.NEXT_PUBLIC_CDN_URL || ''}/blog/images/${src}`;

  return (
    <figure className={cn('my-6', className)}>
      <div className="relative overflow-hidden rounded-lg bg-muted">
        <Image
          src={imageUrl}
          alt={alt}
          width={width}
          height={height}
          className="w-full h-auto object-cover"
          priority={priority}
        />
      </div>
      {caption && (
        <figcaption className="text-sm text-muted-foreground text-center mt-2 italic">
          {caption}
        </figcaption>
      )}
    </figure>
  );
}
