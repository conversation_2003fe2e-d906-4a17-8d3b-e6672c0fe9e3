'use client';

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import type { BlogTemplate, BlogTemplateConfig } from '@/types/blog';

interface BlogLayoutProps {
  children: ReactNode;
  template?: BlogTemplate;
  style?: {
    headerStyle?: 'clean' | 'gradient' | 'minimal' | 'image';
    contentWidth?: 'narrow' | 'normal' | 'wide';
    showToc?: boolean;
    showAuthor?: boolean;
    showDate?: boolean;
    showTags?: boolean;
    showCategory?: boolean;
  };
  className?: string;
}

const templateConfigs: Record<BlogTemplate, BlogTemplateConfig> = {
  default: {
    layout: 'single-column',
    headerStyle: 'clean',
    typography: 'comfortable',
    spacing: 'normal',
  },
  tutorial: {
    layout: 'with-sidebar',
    headerStyle: 'step-indicator',
    typography: 'readable',
    spacing: 'loose',
    features: ['toc', 'progress-bar', 'next-prev'],
  },
  product: {
    layout: 'single-column',
    headerStyle: 'gradient',
    typography: 'comfortable',
    spacing: 'normal',
    features: ['highlight-box'],
  },
  technical: {
    layout: 'wide',
    headerStyle: 'minimal',
    typography: 'code-friendly',
    spacing: 'compact',
    features: ['code-copy', 'line-numbers'],
  },
  minimal: {
    layout: 'single-column',
    headerStyle: 'minimal',
    typography: 'readable',
    spacing: 'loose',
  },
};

export function BlogLayout({ 
  children, 
  template = 'default', 
  style = {},
  className 
}: BlogLayoutProps) {
  const config = templateConfigs[template];
  
  const getContentWidth = () => {
    const width = style.contentWidth || 'normal';
    switch (width) {
      case 'narrow':
        return 'max-w-2xl';
      case 'wide':
        return 'max-w-6xl';
      default:
        return 'max-w-4xl';
    }
  };

  const getLayoutClasses = () => {
    switch (config.layout) {
      case 'with-sidebar':
        return 'grid grid-cols-1 lg:grid-cols-4 gap-8';
      case 'wide':
        return 'w-full';
      default:
        return 'flex flex-col';
    }
  };

  const getTypographyClasses = () => {
    switch (config.typography) {
      case 'readable':
        return 'prose-lg leading-relaxed';
      case 'code-friendly':
        return 'font-mono text-sm leading-normal';
      default:
        return 'leading-normal';
    }
  };

  const getSpacingClasses = () => {
    switch (config.spacing) {
      case 'compact':
        return 'space-y-4';
      case 'loose':
        return 'space-y-8';
      default:
        return 'space-y-6';
    }
  };

  return (
    <div className={cn(
      'min-h-screen bg-background',
      className
    )}>
      <div className={cn(
        'mx-auto px-4 sm:px-6 lg:px-8 py-8',
        getContentWidth()
      )}>
        <div className={cn(
          getLayoutClasses(),
          getSpacingClasses()
        )}>
          {config.layout === 'with-sidebar' ? (
            <>
              {/* 主内容区域 */}
              <main className="lg:col-span-3">
                <article className={cn(
                  'prose prose-gray dark:prose-invert max-w-none',
                  getTypographyClasses()
                )}>
                  {children}
                </article>
              </main>
              
              {/* 侧边栏 */}
              <aside className="lg:col-span-1">
                <div className="sticky top-8 space-y-6">
                  {style.showToc && (
                    <div className="bg-muted/50 rounded-lg p-4">
                      <h3 className="font-semibold text-sm mb-3">Table of Contents</h3>
                      {/* TOC 组件将在这里渲染 */}
                      <div id="blog-toc" className="text-sm space-y-2">
                        {/* TOC 内容将通过 JavaScript 动态生成 */}
                      </div>
                    </div>
                  )}
                  
                  {config.features?.includes('progress-bar') && (
                    <div className="bg-muted/50 rounded-lg p-4">
                      <h3 className="font-semibold text-sm mb-3">Reading Progress</h3>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          id="reading-progress" 
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: '0%' }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </aside>
            </>
          ) : (
            <main className="w-full">
              <article className={cn(
                'prose prose-gray dark:prose-invert mx-auto',
                getTypographyClasses(),
                config.layout === 'wide' ? 'max-w-none' : 'max-w-4xl'
              )}>
                {children}
              </article>
            </main>
          )}
        </div>
      </div>
    </div>
  );
}
