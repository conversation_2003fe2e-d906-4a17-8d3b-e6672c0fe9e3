"use client";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Copy, Play } from "lucide-react";
import { toast } from "sonner";
import { PromptExample, PromptExamplesConfig } from "@/types/blocks/prompt-examples";
import { useState } from "react";
import Image from "next/image";

interface ExampleCardProps {
  example: PromptExample;
  config: PromptExamplesConfig;
}

export default function ExampleCard({ example, config }: ExampleCardProps) {
  const [isVideoOpen, setIsVideoOpen] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(example.prompt || "");
      toast.success(config.successCopied);
    } catch (error) {
      toast.error(config.errorCopyFailed);
    }
  };

  // 从视频URL生成缩略图URL（假设缩略图URL规律）
  const getThumbnailUrl = (videoUrl: string) => {
    if (example.thumbnail) {
      return example.thumbnail;
    }
    // 如果没有提供缩略图，尝试生成一个默认缩略图URL
    // 这里可以根据你的视频服务规律来调整
    return videoUrl.replace('.mp4', '-thumbnail.jpg');
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-4">
        <h3 className="text-lg font-semibold text-center">{example.title}</h3>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 视频缩略图区域 */}
        <Dialog open={isVideoOpen} onOpenChange={setIsVideoOpen}>
          <DialogTrigger asChild>
            <div className="relative aspect-video rounded-lg overflow-hidden bg-muted cursor-pointer group">
              <Image
                src={getThumbnailUrl(example.videoUrl)}
                alt={example.title}
                fill
                unoptimized={true}
                className="object-cover transition-transform group-hover:scale-105"
                onError={(e) => {
                  e.currentTarget.src = '/imgs/placeholder.png';
                }}
              />
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center group-hover:bg-black/30 transition-colors">
                <div className="bg-white/90 rounded-full p-3 group-hover:bg-white transition-colors">
                  <Play className="h-6 w-6 text-gray-800" />
                </div>
              </div>
            </div>
          </DialogTrigger>
          
          <DialogContent className="max-w-4xl">
            <div className="aspect-video rounded-lg overflow-hidden">
              <video 
                src={example.videoUrl}
                controls
                autoPlay
                className="w-full h-full object-cover"
                preload="metadata"
              >
                您的浏览器不支持视频播放。
              </video>
            </div>
          </DialogContent>
        </Dialog>
        
        {/* 提示词区域 */}
        {example.prompt && (
          <div className="space-y-3">
            <div className="relative">
              <pre className="bg-muted border rounded-lg p-4 text-sm overflow-x-auto whitespace-pre-wrap max-h-40 overflow-y-auto text-foreground">
                <code className="text-foreground">{example.prompt}</code>
              </pre>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCopy}
                className="absolute top-2 right-2 h-8 w-8 p-0"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="text-center">
              <Button
                onClick={handleCopy}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Copy className="h-4 w-4 mr-2" />
                {config.copyButton}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 
