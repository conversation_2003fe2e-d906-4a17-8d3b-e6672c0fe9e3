import React from 'react';
import { cn } from '@/lib/utils';

export interface VideoTemplate {
  id: string;
  title: string;
  prompt: string;
  exampleVideoUrl: string;
}

interface TemplateSelectorProps {
  templates: VideoTemplate[];
  selectedTemplateId?: string;
  onTemplateSelect: (template: VideoTemplate) => void;
}

export function TemplateSelector({ 
  templates, 
  selectedTemplateId, 
  onTemplateSelect 
}: TemplateSelectorProps) {
  return (
    <div className="w-full">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {templates.map((template) => (
          <button
            key={template.id}
            type="button"
            onClick={() => onTemplateSelect(template)}
            className={cn(
              "w-full p-3 text-left border rounded-lg transition-all duration-200",
              "hover:shadow-md hover:border-primary/50",
              "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2",
              selectedTemplateId === template.id
                ? "border-primary bg-primary/5"
                : "border-border bg-background"
            )}
          >
            <div className="text-sm font-medium text-foreground">
              {template.title}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}