'use client';

import { Mail, Twitter } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ContactPageTranslations } from '@/types/pages/contact';

interface ContactProps {
  data: ContactPageTranslations;
}

export default function Contact({ data: t }: ContactProps) {
  const handleEmailClick = () => {
    window.location.href = `mailto:${t.email.address}`;
  };

  const handleTwitterClick = () => {
    window.open(t.twitter.url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      {/* Header Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight mb-4">
          {t.title}
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {t.subtitle}
        </p>
      </div>

      {/* Contact Information */}
      <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
        {/* Email Contact Card */}
        <Card className="transition-all duration-200 hover:shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Mail className="h-6 w-6 text-primary" />
              {t.email.title}
            </CardTitle>
            <CardDescription>
              {t.email.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="text-lg font-medium">{t.email.address}</div>
              <Button onClick={handleEmailClick} className="w-full">
                <Mail className="h-4 w-4 mr-2" />
                {t.email.cta}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Twitter Contact Card */}
        <Card className="transition-all duration-200 hover:shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Twitter className="h-6 w-6 text-primary" />
              {t.twitter.title}
            </CardTitle>
            <CardDescription>
              {t.twitter.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="text-lg font-medium">{t.twitter.handle}</div>
              <Button onClick={handleTwitterClick} variant="outline" className="w-full">
                <Twitter className="h-4 w-4 mr-2" />
                {t.twitter.cta}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 
