# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Start dev server**: `pnpm dev` (with turbopack)
- **Build**: `pnpm build`
- **Lint**: `pnpm lint`
- **Start production**: `pnpm start`
- **Bundle analysis**: `pnpm analyze`
- **Cloudflare preview**: `pnpm cf:preview`
- **Cloudflare deploy**: `pnpm cf:deploy`

## Architecture Overview

### Core Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn UI
- **Auth**: next-auth v5 (beta)
- **I18n**: next-intl
- **Database**: Supabase
- **Payments**: Stripe

### Key Application Areas

#### 1. AI Video Generation
- **Veo 3 Integration**: `/app/[locale]/(default)/veo-3-video-generator/page.tsx`
- **API Routes**: `/app/api/generate/veo-3/` (generation, status, callback)
- **Core Logic**: `/aisdk/` (AI SDK providers and models)

#### 2. Prompt Management
- **Categories**: `/api/prompt/categories/[uuid]`
- **Favorites**: `/api/prompt/favorites/[uuid]`
- **UI Components**: `/components/blocks/prompt-examples/`

#### 3. User Management
- **Credits**: `/api/get-user-credits`
- **Orders**: `/api/checkout` (Stripe)
- **Admin**: `/app/[locale]/(admin)/admin/` (users, orders, posts)

#### 4. Content Management
- **Blog**: `/app/[locale]/(default)/posts/` (MDX support)
- **About/Contact**: Static MDX pages
- **Landing**: `/i18n/pages/landing/` (i18n content)

### Code Organization

#### `/app/`
- `[locale]/`: Internationalized routes
- `api/`: REST API endpoints
- `globals.css` & `theme.css`: Global styles

#### `/components/`
- `blocks/`: Landing page sections
- `ui/`: Reusable Shadcn components
- `console/`: Dashboard components
- `dashboard/`: Admin interface

#### `/models/` & `/services/`
- **Models**: Database schema and operations
- **Services**: Business logic layer

#### `/i18n/`
- `messages/`: Global translations
- `pages/`: Page-specific translations

### Environment Setup

Required env vars (see `.env.example`):
- Database: `SUPABASE_URL`, `SUPABASE_ANON_KEY`
- Auth: `NEXTAUTH_SECRET`, `GOOGLE_ID`, `GOOGLE_SECRET`
- AI: `OPENAI_API_KEY`, various AI provider keys
- Payments: `STRIPE_PUBLISHABLE_KEY`, `STRIPE_SECRET_KEY`
- Storage: `AWS_S3_*` for file uploads

### Key Configuration Files
- `next.config.mjs`: Next.js + MDX + i18n setup
- `middleware.ts`: Locale routing
- `components.json`: Shadcn UI config
- `tsconfig.json`: TypeScript paths